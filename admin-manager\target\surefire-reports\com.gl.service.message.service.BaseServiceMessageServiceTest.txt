-------------------------------------------------------------------------------
Test set: com.gl.service.message.service.BaseServiceMessageServiceTest
-------------------------------------------------------------------------------
Tests run: 13, Failures: 4, Errors: 0, Skipped: 0, Time elapsed: 1.7 s <<< FAILURE! - in com.gl.service.message.service.BaseServiceMessageServiceTest
testList_WithDatabaseException_ShouldThrowException  Time elapsed: 0.06 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 应抛出数据库异常 ==> Expected java.lang.RuntimeException to be thrown, but nothing was thrown.
	at com.gl.service.message.service.BaseServiceMessageServiceTest.testList_WithDatabaseException_ShouldThrowException(BaseServiceMessageServiceTest.java:238)

testList_WithZeroCount_ShouldReturnEmptyResult  Time elapsed: 0.016 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    contains("where d.content like"),
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select d.id,dwu.avatar,dwu.nickname,dwu.phone,d.content,d.message_time from dub_base_service_message d left join dub_wechat_user dwu on d.user_id = dwu.id where d.content like ? ) t",
    class java.lang.Long,
    "%测试留言%"
);
-> at com.gl.service.message.service.BaseServiceMessageService.list(BaseServiceMessageService.java:45)

	at com.gl.service.message.service.BaseServiceMessageServiceTest.testList_WithZeroCount_ShouldReturnEmptyResult(BaseServiceMessageServiceTest.java:183)

testList_WithoutSearchCondition_ShouldReturnSuccessResult  Time elapsed: 0.006 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 结果列表应匹配 ==> expected: <[BaseServiceMessageVo(id=1, userId=100, content=这是一条测试留言, messageTime=Fri Aug 01 09:40:46 CST 2025, avatar=http://example.com/avatar.jpg)]> but was: <[]>
	at com.gl.service.message.service.BaseServiceMessageServiceTest.testList_WithoutSearchCondition_ShouldReturnSuccessResult(BaseServiceMessageServiceTest.java:137)

testList_WithSearchCondition_ShouldReturnSuccessResult  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应匹配 ==> expected: <1> but was: <0>
	at com.gl.service.message.service.BaseServiceMessageServiceTest.testList_WithSearchCondition_ShouldReturnSuccessResult(BaseServiceMessageServiceTest.java:102)

